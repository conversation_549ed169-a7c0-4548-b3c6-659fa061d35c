<template>
  <div class="custom-auto-complete" ref="containerRef">
    <a-input
      v-model:value="inputValue"
      :placeholder="placeholder"
      :allowClear="allowClear"
      :disabled="disabled"
      :loading="loading"
      @input="handleInput"
      @keydown="handleKeyDown"
      @focus="handleFocus"
      @blur="handleBlur"
      @clear="handleClear"
      ref="inputRef"
    />
    
    <!-- 下拉选项列表 -->
    <div 
      v-if="showDropdown && filteredOptions.length > 0" 
      class="dropdown-panel"
      :style="dropdownStyle"
    >
      <div
        v-for="(option, index) in filteredOptions"
        :key="option.value"
        :class="[
          'dropdown-option',
          { 'dropdown-option-active': index === activeIndex }
        ]"
        @click="handleOptionClick(option, index)"
        @mouseenter="activeIndex = index"
      >
        <slot name="option" :option="option" :index="index">
          <div class="option-content">
            <div class="option-label">{{ option.label }}</div>
            <div v-if="option.description" class="option-description">
              {{ option.description }}
            </div>
          </div>
        </slot>
      </div>
    </div>
    
    <!-- 无数据提示 -->
    <div 
      v-if="showDropdown && filteredOptions.length === 0 && inputValue.trim()" 
      class="dropdown-panel no-data"
      :style="dropdownStyle"
    >
      <div class="dropdown-option disabled">
        {{ loading ? '搜索中...' : '暂无数据' }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';

interface AutoCompleteOption {
  value: string;
  label: string;
  description?: string;
  data?: any;
}

interface Props {
  value?: string;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
  loading?: boolean;
  options?: AutoCompleteOption[];
  filterOption?: boolean;
}

interface Emits {
  (e: 'update:value', value: string): void;
  (e: 'search', value: string): void;
  (e: 'select', value: string, option: AutoCompleteOption): void;
  (e: 'change', value: string): void;
  (e: 'focus', event: FocusEvent): void;
  (e: 'blur', event: FocusEvent): void;
  (e: 'clear'): void;
  (e: 'keydown', event: KeyboardEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入',
  allowClear: true,
  disabled: false,
  loading: false,
  options: () => [],
  filterOption: true,
});

const emit = defineEmits<Emits>();

const containerRef = ref<HTMLElement>();
const inputRef = ref<any>();
const inputValue = ref<string>('');
const showDropdown = ref<boolean>(false);
const activeIndex = ref<number>(-1);
const isComposing = ref<boolean>(false);

// 监听外部传入的value变化
watch(
  () => props.value,
  (newVal) => {
    if (newVal !== inputValue.value) {
      inputValue.value = newVal || '';
    }
  },
  { immediate: true }
);

// 监听输入值变化，向外部发送更新
watch(inputValue, (newVal) => {
  emit('update:value', newVal);
});

// 过滤后的选项
const filteredOptions = computed(() => {
  if (!props.filterOption || !inputValue.value.trim()) {
    return props.options;
  }
  
  const searchValue = inputValue.value.toLowerCase();
  return props.options.filter(option => 
    option.label.toLowerCase().includes(searchValue) ||
    option.value.toLowerCase().includes(searchValue) ||
    (option.description && option.description.toLowerCase().includes(searchValue))
  );
});

// 下拉框样式
const dropdownStyle = computed(() => {
  return {
    position: 'absolute',
    top: '100%',
    left: '0',
    right: '0',
    zIndex: 1050,
  };
});

/**
 * 输入处理
 */
const handleInput = (event: Event) => {
  if (isComposing.value) return;
  
  const target = event.target as HTMLInputElement;
  const value = target.value;
  
  inputValue.value = value;
  activeIndex.value = -1;
  showDropdown.value = true;
  
  emit('search', value);
  emit('change', value);
};

/**
 * 键盘事件处理
 */
const handleKeyDown = (event: KeyboardEvent) => {
  emit('keydown', event);
  
  if (!showDropdown.value || filteredOptions.value.length === 0) {
    return;
  }
  
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      activeIndex.value = Math.min(activeIndex.value + 1, filteredOptions.value.length - 1);
      break;
      
    case 'ArrowUp':
      event.preventDefault();
      activeIndex.value = Math.max(activeIndex.value - 1, -1);
      break;
      
    case 'Enter':
      event.preventDefault();
      if (activeIndex.value >= 0) {
        handleOptionClick(filteredOptions.value[activeIndex.value], activeIndex.value);
      }
      break;
      
    case 'Escape':
      event.preventDefault();
      showDropdown.value = false;
      activeIndex.value = -1;
      break;
  }
};

/**
 * 选项点击处理
 */
const handleOptionClick = (option: AutoCompleteOption, index: number) => {
  inputValue.value = option.label;
  showDropdown.value = false;
  activeIndex.value = -1;
  
  emit('select', option.value, option);
  emit('change', option.label);
  
  // 聚焦输入框
  nextTick(() => {
    inputRef.value?.focus();
  });
};

/**
 * 焦点处理
 */
const handleFocus = (event: FocusEvent) => {
  showDropdown.value = true;
  emit('focus', event);
};

/**
 * 失焦处理
 */
const handleBlur = (event: FocusEvent) => {
  // 延迟隐藏下拉框，以便点击选项时能正常触发
  setTimeout(() => {
    showDropdown.value = false;
    activeIndex.value = -1;
  }, 200);
  
  emit('blur', event);
};

/**
 * 清空处理
 */
const handleClear = () => {
  inputValue.value = '';
  showDropdown.value = false;
  activeIndex.value = -1;
  
  emit('clear');
  emit('change', '');
};

/**
 * 点击外部关闭下拉框
 */
const handleClickOutside = (event: MouseEvent) => {
  if (containerRef.value && !containerRef.value.contains(event.target as Node)) {
    showDropdown.value = false;
    activeIndex.value = -1;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
});
</script>

<style lang="less" scoped>
.custom-auto-complete {
  position: relative;
  width: 100%;
}

.dropdown-panel {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  max-height: 256px;
  overflow-y: auto;
  
  &.no-data {
    .dropdown-option {
      color: rgba(0, 0, 0, 0.25);
      cursor: default;
    }
  }
}

.dropdown-option {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.3s;
  
  &:hover,
  &.dropdown-option-active {
    background-color: #f5f5f5;
  }
  
  &.disabled {
    cursor: not-allowed;
    color: rgba(0, 0, 0, 0.25);
    
    &:hover {
      background-color: transparent;
    }
  }
}

.option-content {
  .option-label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 1.5;
  }
  
  .option-description {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    line-height: 1.4;
    margin-top: 2px;
  }
}
</style>
