<template>
  <div class="zy-risk-factor-custom-autocomplete">
    <CustomAutoComplete
      v-model:value="inputValue"
      :placeholder="placeholder"
      :allowClear="allowClear"
      :disabled="disabled"
      :loading="loading"
      :options="options"
      :filterOption="false"
      @search="handleSearch"
      @select="handleSelect"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
      @clear="handleClear"
      @keydown="handleKeyDown"
      ref="autoCompleteRef"
    >
      <template #option="{ option }">
        <div class="risk-factor-option">
          <div class="risk-factor-name">{{ option.data.name }}</div>
          <div class="risk-factor-details">
            <span v-if="option.data.code && config.showCode" class="risk-factor-code">
              编码: {{ option.data.code }}
            </span>
            <span v-if="option.data.helpChar && config.showHelpChar" class="risk-factor-help-char">
              {{ option.data.helpChar }}
            </span>
            <span v-if="option.data.wubiChar && config.showWubiChar" class="risk-factor-wubi-char">
              五笔: {{ option.data.wubiChar }}
            </span>
          </div>
        </div>
      </template>
    </CustomAutoComplete>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onMounted, nextTick } from 'vue';
import { debounce } from 'lodash-es';
import { useMessage } from '/@/hooks/web/useMessage';
import {
  getZyRiskFactorAutoComplete,
  exactMatchZyRiskFactorByName,
  getFallbackZyRiskFactor,
  saveOrUpdate
} from '/@/api/basicinfo/zyRiskFactor';
import { cleanRiskFactorData, detectDataIssues } from '/@/utils/zyRiskFactorUtils';
import type {
  ZyRiskFactorAutoCompleteDTO,
  ZyRiskFactorSimpleConfig,
  ZyRiskFactorSimpleChangeEvent,
} from '/@/types/basicinfo/zyRiskFactor';
import CustomAutoComplete from './CustomAutoComplete.vue';

interface AutoCompleteOption {
  value: string;
  label: string;
  description?: string;
  data: ZyRiskFactorAutoCompleteDTO;
}

interface Props {
  value?: string;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
  config?: Partial<ZyRiskFactorSimpleConfig>;
}

interface Emits {
  (e: 'update:value', value: string): void;
  (e: 'change', event: ZyRiskFactorSimpleChangeEvent): void;
  (e: 'select', value: string, option: ZyRiskFactorAutoCompleteDTO): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入危害因素名称、编码或助记码',
  allowClear: true,
  disabled: false,
  config: () => ({}),
});

const emit = defineEmits<Emits>();
const { createMessage } = useMessage();

// 默认配置
const defaultConfig: ZyRiskFactorSimpleConfig = {
  enableAutoAdd: true,
  searchDebounce: 300,
  maxOptions: 50,
  showCode: true,
  showHelpChar: true,
  showWubiChar: false,
};

// 合并配置
const config = computed(() => ({ ...defaultConfig, ...props.config }));

const autoCompleteRef = ref<any>();
const inputValue = ref<string>('');
const options = ref<AutoCompleteOption[]>([]);
const loading = ref<boolean>(false);
const riskFactorList = ref<ZyRiskFactorAutoCompleteDTO[]>([]);
const isManualInput = ref<boolean>(false);
const selectedFactors = ref<ZyRiskFactorAutoCompleteDTO[]>([]);

// 监听外部传入的value变化
watch(
  () => props.value,
  async (newVal) => {
    if (newVal !== inputValue.value) {
      inputValue.value = newVal || '';
      if (newVal) {
        await syncSelectedFactorsFromInput(newVal);
      } else {
        selectedFactors.value = [];
      }
    }
  },
  { immediate: true }
);

// 监听输入值变化，向外部发送更新
watch(inputValue, (newVal) => {
  emit('update:value', newVal);
});

/**
 * 获取危害因素列表数据
 */
const fetchRiskFactorList = async (keyword?: string, searchType: 'name' | 'code' | 'helpChar' | 'wubiChar' | 'all' = 'all') => {
  try {
    loading.value = true;
    console.log('ZyRiskFactorCustomAutoComplete: 开始获取危害因素列表，关键词:', keyword, '搜索类型:', searchType);

    const response = await getZyRiskFactorAutoComplete(keyword, config.value.maxOptions, searchType);
    console.log('ZyRiskFactorCustomAutoComplete: API响应:', response);

    if (response && response.success && response.result) {
      riskFactorList.value = response.result;
      updateOptions(keyword);
      console.log('ZyRiskFactorCustomAutoComplete: 成功加载', response.result.length, '条数据');

      if (response.message && response.message.includes('降级模式')) {
        console.info('ZyRiskFactorCustomAutoComplete: 使用降级模式加载数据');
      }
    } else {
      console.warn('ZyRiskFactorCustomAutoComplete: API响应格式异常:', response);
      riskFactorList.value = [];
      options.value = [];
    }
  } catch (error: any) {
    console.error('ZyRiskFactorCustomAutoComplete: 获取危害因素列表失败:', error);
    riskFactorList.value = [];
    options.value = [];
  } finally {
    loading.value = false;
  }
};

/**
 * 更新选项列表
 */
const updateOptions = (keyword?: string) => {
  const { cleaned, filtered } = cleanRiskFactorData(riskFactorList.value, keyword);

  if (filtered.length > 0) {
    console.warn('ZyRiskFactorCustomAutoComplete: 过滤了以下无效数据:', filtered);
  }

  const dataIssues = detectDataIssues(cleaned);
  if (dataIssues.issues.length > 0) {
    console.warn('ZyRiskFactorCustomAutoComplete: 检测到数据质量问题:', dataIssues.issues);
  }

  options.value = cleaned.map((item) => ({
    value: item.id,
    label: item.name,
    data: item,
  }));

  console.log('ZyRiskFactorCustomAutoComplete: 更新选项列表，原始数据', riskFactorList.value.length, '条，清理后', options.value.length, '个选项');
};

/**
 * 提取最后一个词作为搜索关键词
 */
const extractLastWord = (input: string): string => {
  if (!input) return '';
  const words = input.trim().split(/\s+/);
  return words[words.length - 1] || '';
};

/**
 * 设置光标位置到输入框末尾
 */
const setCursorToEnd = () => {
  nextTick(() => {
    if (autoCompleteRef.value) {
      autoCompleteRef.value.focus();
    }
  });
};

/**
 * 添加危害因素到已选中列表
 */
const addToSelectedFactors = (factor: ZyRiskFactorAutoCompleteDTO) => {
  const existingIndex = selectedFactors.value.findIndex(f => f.name === factor.name);
  if (existingIndex >= 0) {
    selectedFactors.value[existingIndex] = factor;
  } else {
    selectedFactors.value.push(factor);
  }
};

/**
 * 过滤掉临时数据，只返回有效的危害因素
 */
const getValidSelectedFactors = (): ZyRiskFactorAutoCompleteDTO[] => {
  return selectedFactors.value.filter(factor =>
    !factor.id.startsWith('temp_') || factor.code.startsWith('AUTO_')
  );
};

/**
 * 根据输入值同步已选中的危害因素列表
 */
const syncSelectedFactorsFromInput = async (inputStr: string) => {
  if (!inputStr.trim()) {
    selectedFactors.value = [];
    return;
  }

  const words = inputStr.trim().split(/\s+/);
  const newSelectedFactors: ZyRiskFactorAutoCompleteDTO[] = [];

  for (const word of words) {
    const existing = selectedFactors.value.find(f => f.name === word);
    if (existing) {
      newSelectedFactors.push(existing);
    } else {
      try {
        const exactMatchResponse = await exactMatchZyRiskFactorByName(word);
        if (exactMatchResponse?.success && exactMatchResponse.result) {
          const matchedFactor = exactMatchResponse.result;
          newSelectedFactors.push({
            id: matchedFactor.id,
            name: matchedFactor.name,
            code: matchedFactor.code,
            helpChar: matchedFactor.helpChar,
            wubiChar: matchedFactor.wubiChar,
          });
        } else {
          console.warn(`未找到危害因素: ${word}，将在确认时处理`);
          newSelectedFactors.push({
            id: `temp_${word}`,
            name: word,
            code: `TEMP_${Date.now()}`,
            helpChar: '',
            wubiChar: '',
          });
        }
      } catch (error) {
        console.warn(`查找危害因素失败: ${word}`, error);
        newSelectedFactors.push({
          id: `temp_${word}`,
          name: word,
          code: `TEMP_${Date.now()}`,
          helpChar: '',
          wubiChar: '',
        });
      }
    }
  }

  selectedFactors.value = newSelectedFactors;
};

/**
 * 防抖搜索处理
 */
const debouncedSearch = debounce(async (keyword: string) => {
  await fetchRiskFactorList(keyword);
}, config.value.searchDebounce);

/**
 * 搜索处理
 */
const handleSearch = (value: string) => {
  isManualInput.value = true;

  if (!value || value.trim().length === 0) {
    fetchRiskFactorList();
  } else {
    const searchKeyword = extractLastWord(value);
    if (searchKeyword) {
      debouncedSearch(searchKeyword);
    } else {
      fetchRiskFactorList();
    }
  }
};

/**
 * 选择处理 - 点击选择
 */
const handleSelect = (value: string, option: AutoCompleteOption) => {
  const selectedFactor = option.data;
  isManualInput.value = false;

  const currentInput = inputValue.value;
  let newValue: string;

  // 点击选中：判断是否为多词输入
  const trimmed = currentInput.trim();
  if (trimmed.includes(' ')) {
    // 多词输入：替换最后一个词
    const words = trimmed.split(/\s+/);
    words[words.length - 1] = selectedFactor.name;
    newValue = words.join(' ');
  } else {
    // 单词输入：直接显示选中的名称
    newValue = selectedFactor.name;
  }

  inputValue.value = newValue;
  addToSelectedFactors(selectedFactor);
  setCursorToEnd();

  const event: ZyRiskFactorSimpleChangeEvent = {
    value: newValue,
    selectedFactor,
    selectedFactors: getValidSelectedFactors(),
    isManualInput: false,
    isNewlyCreated: false,
  };
  emit('change', event);
  emit('select', value, selectedFactor);
};

/**
 * 值变化处理
 */
const handleChange = (value: string) => {
  if (!value) {
    selectedFactors.value = [];
    const event: ZyRiskFactorSimpleChangeEvent = {
      value: '',
      selectedFactors: [],
      isManualInput: false,
    };
    emit('change', event);
  }
};

/**
 * 焦点处理
 */
const handleFocus = (event: FocusEvent) => {
  // 可以在这里添加焦点相关的逻辑
};

/**
 * 失焦处理
 */
const handleBlur = async (event: FocusEvent) => {
  if (!isManualInput.value || !inputValue.value.trim() || !config.value.enableAutoAdd) {
    return;
  }

  try {
    await handleAutoAdd();
  } catch (error: any) {
    console.error('自动添加处理失败:', error);
    createMessage.error('自动添加功能暂时不可用');
  }
};

/**
 * 清空处理
 */
const handleClear = () => {
  inputValue.value = '';
  selectedFactors.value = [];
  isManualInput.value = false;

  const event: ZyRiskFactorSimpleChangeEvent = {
    value: '',
    selectedFactors: [],
    isManualInput: false,
  };
  emit('change', event);

  fetchRiskFactorList();
};

/**
 * 键盘事件处理 - 这里是我们的核心逻辑
 */
const handleKeyDown = (event: KeyboardEvent) => {
  // 空格键处理逻辑 - 追加模式
  if (event.key === ' ' && isManualInput.value && inputValue.value.trim()) {
    event.preventDefault();

    if (options.value.length > 0) {
      const firstOption = options.value[0];
      const selectedFactor = firstOption.data;
      const currentInput = inputValue.value;

      // 空格键选择 - 追加模式
      const words = currentInput.trim().split(/\s+/);
      words[words.length - 1] = selectedFactor.name;
      const newValue = words.join(' ') + ' '; // 末尾添加空格

      inputValue.value = newValue;
      addToSelectedFactors(selectedFactor);
      setCursorToEnd();

      const changeEvent: ZyRiskFactorSimpleChangeEvent = {
        value: newValue,
        selectedFactor,
        selectedFactors: getValidSelectedFactors(),
        isManualInput: false,
        isNewlyCreated: false,
      };
      emit('change', changeEvent);
      emit('select', selectedFactor.id, selectedFactor);
    } else {
      handleAutoAdd();
    }
  }

  // 回车键处理逻辑 - 追加模式
  if (event.key === 'Enter' && isManualInput.value && inputValue.value.trim()) {
    event.preventDefault();

    if (options.value.length > 0) {
      const firstOption = options.value[0];
      const selectedFactor = firstOption.data;
      const currentInput = inputValue.value;

      // 回车键选择 - 追加模式
      const words = currentInput.trim().split(/\s+/);
      words[words.length - 1] = selectedFactor.name;
      const newValue = words.join(' ') + ' '; // 末尾添加空格

      inputValue.value = newValue;
      addToSelectedFactors(selectedFactor);
      setCursorToEnd();

      const changeEvent: ZyRiskFactorSimpleChangeEvent = {
        value: newValue,
        selectedFactor,
        selectedFactors: getValidSelectedFactors(),
        isManualInput: false,
        isNewlyCreated: false,
      };
      emit('change', changeEvent);
      emit('select', selectedFactor.id, selectedFactor);
    } else {
      handleAutoAdd();
    }
  }
};

/**
 * 处理自动添加新数据
 */
const handleAutoAdd = async () => {
  const input = inputValue.value.trim();
  if (!input) return;

  const lastWord = extractLastWord(input);
  if (!lastWord) return;

  try {
    loading.value = true;

    // 1. 先检查最后一个词是否已存在
    let exactMatchResponse: any = null;

    try {
      exactMatchResponse = await exactMatchZyRiskFactorByName(lastWord);
    } catch (error: any) {
      console.warn('精确匹配API调用失败:', error);
    }

    if (exactMatchResponse?.success && exactMatchResponse.result) {
      // 如果找到精确匹配
      const matchedFactor = exactMatchResponse.result;
      const trimmedInput = input.trim();
      let newValue: string;

      if (trimmedInput.includes(' ')) {
        const words = trimmedInput.split(/\s+/);
        words[words.length - 1] = matchedFactor.name;
        newValue = words.join(' ');
      } else {
        newValue = matchedFactor.name;
      }

      inputValue.value = newValue;
      await syncSelectedFactorsFromInput(newValue);
      addToSelectedFactors(matchedFactor);
      setCursorToEnd();

      const event: ZyRiskFactorSimpleChangeEvent = {
        value: newValue,
        selectedFactor: matchedFactor,
        selectedFactors: getValidSelectedFactors(),
        isManualInput: true,
        isNewlyCreated: false,
      };
      emit('change', event);
      createMessage.success('找到匹配的危害因素');
      return;
    }

    // 2. 如果不存在，获取fallback模板并创建新记录
    let fallbackData: ZyRiskFactorAutoCompleteDTO;

    try {
      const fallbackResponse = await getFallbackZyRiskFactor();
      if (fallbackResponse.success && fallbackResponse.result) {
        fallbackData = fallbackResponse.result;
      } else {
        throw new Error('Fallback API返回失败');
      }
    } catch (error: any) {
      console.warn('获取fallback数据失败，使用默认模板:', error);
      fallbackData = {
        id: 'default_fallback',
        name: '默认模板',
        code: 'DEFAULT',
        helpChar: 'MR',
        wubiChar: 'MRMB',
      };
      createMessage.warning('使用默认模板创建危害因素');
    }

    // 3. 创建新的危害因素记录
    const newRiskFactor = {
      name: lastWord,
      code: 'AUTO_' + Date.now(),
      helpChar: fallbackData.helpChar || '',
      wubiCode: fallbackData.wubiChar || '',
      remark: `基于用户输入"${lastWord}"自动创建，模板来源：${fallbackData.name}`,
      valid: 1,
      sysFlag: '0',
      sort: 999,
      fallbackFlag: '0',
    };

    // 4. 保存新记录
    let newFactorDTO: ZyRiskFactorAutoCompleteDTO;

    try {
      const saveResponse = await saveOrUpdate(newRiskFactor, false);
      if (saveResponse && saveResponse.success) {
        newFactorDTO = {
          id: saveResponse.result?.id || Date.now().toString(),
          name: lastWord,
          code: newRiskFactor.code,
          helpChar: newRiskFactor.helpChar,
          wubiChar: newRiskFactor.wubiCode,
        };
      } else {
        throw new Error('保存API返回失败');
      }
    } catch (error: any) {
      console.warn('保存新危害因素失败，使用临时数据:', error);
      newFactorDTO = {
        id: `temp_new_${Date.now()}`,
        name: lastWord,
        code: newRiskFactor.code,
        helpChar: newRiskFactor.helpChar,
        wubiChar: newRiskFactor.wubiCode,
      };
      createMessage.warning(`临时添加危害因素：${lastWord}（未保存到服务器）`);
    }

    // 5. 更新输入框值
    const trimmedInput = input.trim();
    let newValue: string;

    if (trimmedInput.includes(' ')) {
      const words = trimmedInput.split(/\s+/);
      words[words.length - 1] = lastWord;
      newValue = words.join(' ');
    } else {
      newValue = lastWord;
    }

    inputValue.value = newValue;
    await syncSelectedFactorsFromInput(newValue);
    addToSelectedFactors(newFactorDTO);
    setCursorToEnd();

    const event: ZyRiskFactorSimpleChangeEvent = {
      value: newValue,
      selectedFactor: newFactorDTO,
      selectedFactors: getValidSelectedFactors(),
      isManualInput: true,
      isNewlyCreated: true,
    };
    emit('change', event);

    if (newFactorDTO.id.startsWith('temp_new_')) {
      createMessage.warning(`临时添加危害因素：${lastWord}`);
    } else {
      createMessage.success(`已自动添加新的危害因素：${lastWord}`);
      await fetchRiskFactorList();
    }

  } catch (error: any) {
    console.error('自动添加危害因素失败:', error);
    createMessage.error('自动添加失败：' + (error?.message || '未知错误'));
  } finally {
    loading.value = false;
    isManualInput.value = false;
  }
};

// 组件挂载时加载初始数据
onMounted(() => {
  fetchRiskFactorList();
});

// 暴露方法给父组件
defineExpose({
  clearSelection: handleClear,
});
</script>

<style lang="less" scoped>
.zy-risk-factor-custom-autocomplete {
  .risk-factor-option {
    display: flex;
    flex-direction: column;
    padding: 6px 0;

    .risk-factor-name {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 1.4;
      font-weight: 500;
    }

    .risk-factor-details {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 4px;

      .risk-factor-code {
        font-size: 12px;
        color: #1890ff;
        background: #f0f8ff;
        padding: 2px 6px;
        border-radius: 4px;
        line-height: 1.2;
      }

      .risk-factor-help-char {
        font-size: 12px;
        color: #52c41a;
        background: #f6ffed;
        padding: 2px 6px;
        border-radius: 4px;
        line-height: 1.2;
      }

      .risk-factor-wubi-char {
        font-size: 12px;
        color: #722ed1;
        background: #f9f0ff;
        padding: 2px 6px;
        border-radius: 4px;
        line-height: 1.2;
      }
    }
  }
}
</style>
