package org.jeecg.modules.reg.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.entity.Company;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CompanyTeam;
import org.jeecg.modules.reg.entity.CompanyTeamItemGroup;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 单位预约
 * @Author: jeecg-boot
 * @Date: 2024-02-19
 * @Version: V1.0
 */
public interface ICompanyRegService extends IService<CompanyReg> {

    /**
     * 删除一对多
     *
     * @param id
     */
    public void delMain(String id);

    /**
     * 批量删除一对多
     *
     * @param idList
     */
    void delBatchMain(Collection<? extends Serializable> idList);

    boolean teamDuplicateCheck(String teamId, String companyId, String teamName);

    void saveItemGroupOfTeam(String teamId, List<CompanyTeamItemGroup> itemGroupList);

    void saveItemGroupOfTeam(String teamId, List<CompanyTeamItemGroup> itemGroupList, boolean skipGiftAndAttach);

    List<CompanyTeamItemGroup> getItemGroupOfTeam(String teamId);

    CompanyTeam getCompanyTeam(String teamId);

    CompanyReg getCompanyRegDetail(String id);

    List<CompanyReg> pageCompanyReg(Page<CompanyReg> page, String keyword,List<String> id);

    /**
     * 带缓存的单位预约搜索方法
     * @param keyword 搜索关键词
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param ids ID列表
     * @return 查询结果列表
     */
    List<CompanyReg> searchCompanyRegWithCache(String keyword, Integer pageNo, Integer pageSize, List<String> ids);

    List<Company> pageCompanyByPid(Page<Company> page, String keyword, List<String> id, String pid);

    Page<CompanyReg> pageCompanyReg4Occu(Page<CompanyReg> companyPage, String companyRegId, String teamId, String examCategory, String occuReportResultStatus, String occuReportUploadTimeStart, String occuReportUploadTimeEnd, String summaryStatus);
}
