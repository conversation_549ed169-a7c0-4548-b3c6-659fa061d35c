package org.jeecg.modules.reg.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * CompanyReg缓存配置类
 * 实现多层缓存架构：本地缓存(Caffeine) + 分布式缓存(Redis)
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "cache.company-reg")
public class CompanyRegCacheConfig {

    /**
     * 缓存键前缀
     */
    public static final String CACHE_PREFIX = "company_reg:search:";
    
    /**
     * 本地缓存名称
     */
    public static final String LOCAL_CACHE_NAME = "companyRegLocalCache";
    
    /**
     * Redis缓存名称
     */
    public static final String REDIS_CACHE_NAME = "companyRegRedisCache";

    // ==================== 缓存配置参数 ====================
    
    /**
     * 本地缓存配置
     */
    private LocalCacheConfig local = new LocalCacheConfig();
    
    /**
     * Redis缓存配置
     */
    private RedisCacheConfig redis = new RedisCacheConfig();
    
    /**
     * 缓存策略配置
     */
    private CacheStrategyConfig strategy = new CacheStrategyConfig();

    // ==================== 本地缓存Bean ====================
    
    /**
     * 创建Caffeine本地缓存
     */
    @Bean(LOCAL_CACHE_NAME)
    public Cache<String, Object> companyRegLocalCache() {
        log.info("初始化CompanyReg本地缓存，容量：{}，过期时间：{}分钟", 
                local.getMaxSize(), local.getExpireAfterWrite());
        
        return Caffeine.newBuilder()
                // 最大缓存条目数
                .maximumSize(local.getMaxSize())
                // 写入后过期时间
                .expireAfterWrite(local.getExpireAfterWrite(), TimeUnit.MINUTES)
                // 访问后过期时间
                .expireAfterAccess(local.getExpireAfterAccess(), TimeUnit.MINUTES)
                // 初始容量
                .initialCapacity(local.getInitialCapacity())
                // 启用统计
                .recordStats()
                // 移除监听器
                .removalListener((key, value, cause) -> {
                    log.debug("本地缓存移除：key={}, cause={}", key, cause);
                })
                .build();
    }

    // ==================== 缓存键生成策略 ====================
    
    /**
     * 生成缓存键
     * 格式：company_reg:search:{keyword}:{pageNo}:{pageSize}:{ids}
     */
    public static String generateCacheKey(String keyword, Integer pageNo, Integer pageSize, List<String> ids) {
        StringBuilder keyBuilder = new StringBuilder(CACHE_PREFIX);
        
        // 关键词（为空时用"*"表示）
        keyBuilder.append(keyword != null ? keyword.trim() : "*");
        keyBuilder.append(":");
        
        // 分页参数
        keyBuilder.append(pageNo != null ? pageNo : 1);
        keyBuilder.append(":");
        keyBuilder.append(pageSize != null ? pageSize : 10);
        keyBuilder.append(":");
        
        // ID列表（排序后拼接，确保一致性）
        if (ids != null && !ids.isEmpty()) {
            ids.stream().sorted().forEach(id -> keyBuilder.append(id).append(","));
            // 移除最后一个逗号
            keyBuilder.setLength(keyBuilder.length() - 1);
        } else {
            keyBuilder.append("*");
        }
        
        return keyBuilder.toString();
    }
    
    /**
     * 生成模糊匹配键模式
     * 用于批量删除相关缓存
     */
    public static String generateKeyPattern(String keyword) {
        return CACHE_PREFIX + (keyword != null ? keyword.trim() : "*") + ":*";
    }

    // ==================== 配置类 ====================
    
    /**
     * 本地缓存配置
     */
    public static class LocalCacheConfig {
        /**
         * 最大缓存条目数
         */
        private long maxSize = 1000;
        
        /**
         * 写入后过期时间（分钟）
         */
        private long expireAfterWrite = 30;
        
        /**
         * 访问后过期时间（分钟）
         */
        private long expireAfterAccess = 15;
        
        /**
         * 初始容量
         */
        private int initialCapacity = 100;

        // Getters and Setters
        public long getMaxSize() { return maxSize; }
        public void setMaxSize(long maxSize) { this.maxSize = maxSize; }
        
        public long getExpireAfterWrite() { return expireAfterWrite; }
        public void setExpireAfterWrite(long expireAfterWrite) { this.expireAfterWrite = expireAfterWrite; }
        
        public long getExpireAfterAccess() { return expireAfterAccess; }
        public void setExpireAfterAccess(long expireAfterAccess) { this.expireAfterAccess = expireAfterAccess; }
        
        public int getInitialCapacity() { return initialCapacity; }
        public void setInitialCapacity(int initialCapacity) { this.initialCapacity = initialCapacity; }
    }
    
    /**
     * Redis缓存配置
     */
    public static class RedisCacheConfig {
        /**
         * Redis缓存过期时间（分钟）
         */
        private long expireTime = 60;
        
        /**
         * 是否启用Redis缓存
         */
        private boolean enabled = true;

        // Getters and Setters
        public long getExpireTime() { return expireTime; }
        public void setExpireTime(long expireTime) { this.expireTime = expireTime; }
        
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
    }
    
    /**
     * 缓存策略配置
     */
    public static class CacheStrategyConfig {
        /**
         * 是否启用本地缓存
         */
        private boolean localEnabled = true;
        
        /**
         * 是否启用Redis缓存
         */
        private boolean redisEnabled = true;
        
        /**
         * 缓存预热是否启用
         */
        private boolean preloadEnabled = false;
        
        /**
         * 预热数据大小
         */
        private int preloadSize = 50;

        // Getters and Setters
        public boolean isLocalEnabled() { return localEnabled; }
        public void setLocalEnabled(boolean localEnabled) { this.localEnabled = localEnabled; }
        
        public boolean isRedisEnabled() { return redisEnabled; }
        public void setRedisEnabled(boolean redisEnabled) { this.redisEnabled = redisEnabled; }
        
        public boolean isPreloadEnabled() { return preloadEnabled; }
        public void setPreloadEnabled(boolean preloadEnabled) { this.preloadEnabled = preloadEnabled; }
        
        public int getPreloadSize() { return preloadSize; }
        public void setPreloadSize(int preloadSize) { this.preloadSize = preloadSize; }
    }

    // ==================== Getters and Setters ====================
    
    public LocalCacheConfig getLocal() { return local; }
    public void setLocal(LocalCacheConfig local) { this.local = local; }
    
    public RedisCacheConfig getRedis() { return redis; }
    public void setRedis(RedisCacheConfig redis) { this.redis = redis; }
    
    public CacheStrategyConfig getStrategy() { return strategy; }
    public void setStrategy(CacheStrategyConfig strategy) { this.strategy = strategy; }
}
