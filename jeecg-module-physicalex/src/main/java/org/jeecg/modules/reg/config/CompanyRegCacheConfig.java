package org.jeecg.modules.reg.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * CompanyReg缓存配置类
 * 使用Spring Boot缓存注解 + Caffeine本地缓存
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Configuration
@EnableCaching
public class CompanyRegCacheConfig {

    /**
     * 缓存名称常量
     */
    public static final String COMPANY_REG_CACHE = "companyRegCache";

    /**
     * 缓存键生成器名称
     */
    public static final String CACHE_KEY_GENERATOR = "companyRegKeyGenerator";

    /**
     * 配置Caffeine缓存管理器
     */
    @Bean
    @Primary
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        // 设置缓存名称
        cacheManager.setCacheNames(List.of(COMPANY_REG_CACHE));

        // 配置Caffeine
        cacheManager.setCaffeine(Caffeine.newBuilder()
                // 最大缓存条目数
                .maximumSize(1000)
                // 写入后30分钟过期
                .expireAfterWrite(30, TimeUnit.MINUTES)
                // 访问后15分钟过期
                .expireAfterAccess(15, TimeUnit.MINUTES)
                // 初始容量
                .initialCapacity(100)
                // 启用统计
                .recordStats()
                // 移除监听器
                .removalListener((key, value, cause) -> {
                    log.debug("缓存移除：key={}, cause={}", key, cause);
                }));

        log.info("初始化CompanyReg缓存管理器完成");
        return cacheManager;
    }

    /**
     * 自定义缓存键生成器
     * 用于生成复合参数的缓存键
     */
    @Bean(CACHE_KEY_GENERATOR)
    public org.springframework.cache.interceptor.KeyGenerator companyRegKeyGenerator() {
        return (target, method, params) -> {
            StringBuilder keyBuilder = new StringBuilder();

            // 方法名
            keyBuilder.append(method.getName()).append(":");

            // 参数拼接
            for (int i = 0; i < params.length; i++) {
                Object param = params[i];
                if (param != null) {
                    if (param instanceof List) {
                        List<?> list = (List<?>) param;
                        if (!list.isEmpty()) {
                            keyBuilder.append(list.toString());
                        } else {
                            keyBuilder.append("empty");
                        }
                    } else {
                        keyBuilder.append(param.toString());
                    }
                } else {
                    keyBuilder.append("null");
                }

                if (i < params.length - 1) {
                    keyBuilder.append(":");
                }
            }

            return keyBuilder.toString();
        };
    }


}
